// NOVEMBER DISCOUNT DISPLAY

console.log('�️ �🎯 Pricing and slashes script loaded');

// Debounce timeout variable
let handleResultsTimeout;
const discountedProductParents = new Set(); // Store parent elements with discounted prices

// Discount data
const productDiscounts = [
  {"url": "/holidays/kilimanjaro-the-long-way", "discount": 400.0},
  {"url": "/holidays/machu-picchu-via-inca-trail", "discount": 100.0},
  {"url": "/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley", "discount": 100.0},
  {"url": "/holidays/everest-base-camp-trek", "discount": 400.0},
  {"url": "/destinations/asia/nepal/everest-base-camp-expedition", "discount": 400.0},
  {"url": "/holidays/mt-toubkal-roof-of-the-north-weekender", "discount": 50.0},
  {"url": "/holidays/mt-toubkal-roof-of-the-north-8-day-trek", "discount": 50.0},
  {"url": "/holidays/langtang-valley-trek", "discount": 400.0},
  {"url": "/holidays/island-peak-expedition", "discount": 400.0},
  {"url": "/holidays/annapurna-circuit-trek", "discount": 400.0},
  {"url": "/holidays/ultimate-mera-peak-expedition", "discount": 400.0},
  {"url": "/holidays/ultimate-island-peak-and-everest-base-camp-expedition", "discount": 400.0},
  {"url": "/holidays/everest-three-passes-trek", "discount": 400.0},
  {"url": "/holidays/everest-base-camp-via-gokyo-valley", "discount": 400.0},
  {"url": "/holidays/tour-du-mont-blanc-trek", "discount": 400.0},
  {"url": "/holidays/k2-base-camp-trek", "discount": 400.0},
  {"url": "/holidays/gran-paradiso-hut-to-hut-summit-trek", "discount": 400.0},
  {"url": "/holidays/annapurna-base-camp-trek", "discount": 400.0},
  {"url": "/holidays/ultimate-aconcagua", "discount": 400.0}
];

// List of CSS selectors
const cssSelectors = [
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(1) span',
  '.sppb-addon-holiday .zen-card__body .zen-flex-end .row div > .zen-pill:nth-of-type(3)',
  '.zen-holiday__info-bar .col:nth-of-type(4) .zen-text',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(1) .zen-pill',
  '[id *="price-year-item-"] .col:nth-of-type(2) .zen-accordion__content-cell:nth-of-type(3) .zen-pill',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(1) .ng-binding',
  '.search-item .col-md-7 .zen-flex-end .zen-pill:nth-of-type(3)',
];

// Helper functions
function normalizePrice(priceStr, productUrl) {
  console.log('🏷️ 💰 Normalizing price:', priceStr, 'for URL:', productUrl);
  const match = priceStr.match(/£\s*([\d,]+(?:\.\d+)?)(?:\s*\/\s*(\d+))?/);
  if (match) {
    const price = parseFloat(match[1].replace(/,/g, ''));
    const months = match[2] ? parseInt(match[2], 10) : null;
    const result = { price: isNaN(price) ? 0 : price, months };
    console.log('🏷️ 💰 Normalized result:', result);
    return result;
  }
  const fallbackResult = { price: parseFloat(priceStr.replace(/[^0-9.-]+/g, '')), months: null };
  console.log('🏷️ 💰 Fallback normalized result:', fallbackResult);
  return fallbackResult;
}

function formatPriceWithOriginal(priceStr, newPrice) {
  const prefix = '£';
  const suffix = priceStr.match(/pp/i) ? 'pp' : '';
  const formatted = `${prefix}${Math.ceil(newPrice)}${suffix}`;
  console.log('🏷️ 💷 Formatted price:', formatted, 'from:', newPrice);
  return formatted;
}

function calculatePayMonthly(innerHTML, discount, productUrl) {
  console.log('🏷️ 📅 Calculating monthly payment for:', innerHTML, 'discount:', discount);
  const { price: finalPrice, months } = normalizePrice(innerHTML, productUrl);
  const deposit = 200; // Keep for potential future use
  const wasPrice = (finalPrice * (months || 1) + discount) / (months || 1);
  const result = `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}${months ? ` / ${months} Months` : ''}`;
  console.log('🏷️ 📅 Monthly payment result:', result);
  return result;
}

function calculatePayInFull(innerHTML, discount, productUrl) {
  console.log('🏷️ 💳 Calculating full payment for:', innerHTML, 'discount:', discount);
  const { price: finalPrice } = normalizePrice(innerHTML, productUrl);
  const wasPrice = finalPrice + discount;
  const result = `<span style="text-decoration: line-through; opacity: .5">${formatPriceWithOriginal(innerHTML, wasPrice)}</span> ${formatPriceWithOriginal(innerHTML, finalPrice)}`;
  console.log('🏷️ 💳 Full payment result:', result);
  return result;
}

// Process .zen-holiday__content-box only if URL matches productDiscounts
const processZenHolidayContentBox = () => {
// return; // TEMP DISABLE
  console.log('�️ �🏠 Processing zen holiday content box...');
  const currentUrl = window.location.pathname;
  console.log('�️ �🏠 Current URL:', currentUrl);
  const matchingProduct = productDiscounts.find(item => item.url === currentUrl);

  if (matchingProduct) {
    console.log(`🏷️ ✅ URL matches productDiscounts: ${currentUrl}`, matchingProduct);

    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    console.log("🏷️ 🔍 Found appPanels elements:", appPanels.length, appPanels);

    if (appPanels.length > 0) {
      appPanels.forEach((appPanel, index) => {
        console.log(`🏷️ 🔧 Processing appPanel [${index}]:`, appPanel);

        appPanel.style.padding = 0;

        // Log existing children
        console.log("🏷️ 👶 Existing children of appPanel:", appPanel.children.length, Array.from(appPanel.children));

        // Remove all child elements safely
        let removedCount = 0;
        while (appPanel.firstChild) {
          appPanel.removeChild(appPanel.firstChild);
          removedCount++;
        }

        console.log(`🏷️ 🗑️ Removed ${removedCount} children from appPanel`);

        // Add the SVG
        const svgImg = document.createElement('img');
        svgImg.src = '/images/2024/11/08/nov_panel.svg'; // Replace with your SVG URL
        svgImg.alt = 'Icon';
        svgImg.classList.add('app-flash');

        appPanel.appendChild(svgImg);
        console.log("🏷️ 🖼️ SVG added to appPanel:", svgImg.src);
      });
    } else {
      console.warn("🏷️ ⚠️ No matching elements found for '.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media'.");
    }
  } else {
    console.log(`🏷️ ❌ Current URL does not match productDiscounts: ${currentUrl}`);
    console.log('🏷️ 📋 Available product URLs:', productDiscounts.map(p => p.url));
  }
};

// Main function to handle results with debounce
const handleResults = function(eventOrMutations) {
  console.log('🏷️ 🚀 handleResults called with:', eventOrMutations);
  clearTimeout(handleResultsTimeout);

  const debounceDelay = eventOrMutations === "XHR Response Trigger" ? 50 : 0;
  console.log('🏷️ ⏱️ Debounce delay:', debounceDelay + 'ms');

  handleResultsTimeout = setTimeout(() => {
    console.log("🏷️ 🔄 Processing discount prices and corner flashes...");

    // Apply pricing updates
    cssSelectors.forEach((selector, selectorIndex) => {
      console.log(`�️ �🎯 Processing selector [${selectorIndex}]:`, selector);
      const elements = document.querySelectorAll(selector);
      console.log(`�️ �🎯 Found ${elements.length} elements for selector:`, selector);

      elements.forEach((element, elementIndex) => {
        const innerHTML = element.innerHTML.trim();
        console.log(`🏷️ 💡 Processing element [${elementIndex}] innerHTML:`, innerHTML);
        let fullUrl = window.location.pathname;
        console.log('�️ �🌐 Current URL:', fullUrl);

        let product = productDiscounts.find(item => item.url === fullUrl);
        if (!product) {
          console.log('🏷️ 🔍 No direct URL match, checking closest link...');
          const closestLink = element.closest('a[href]');
          if (closestLink) {
            const fallbackUrl = new URL(closestLink.getAttribute('href'), window.location.origin).pathname;
            console.log('🏷️ 🔗 Fallback URL from link:', fallbackUrl);
            product = productDiscounts.find(item => item.url === fallbackUrl);
          }
        }

        if (product) {
          console.log('🏷️ ✅ Product match found:', product);
          const parent = element.closest('.search-item, .sppb-addon-holiday');
          console.log('🏷️ 👨‍👩‍👧‍👦 Adding parent to discounted set:', parent);
          discountedProductParents.add(parent); // Add parent to set

          if (innerHTML.includes('/')) {
            console.log('🏷️ 📅 Processing as monthly payment');
            element.innerHTML = calculatePayMonthly(innerHTML, product.discount, product.url);
          } else {
            console.log('🏷️ 💳 Processing as full payment');
            element.innerHTML = calculatePayInFull(innerHTML, product.discount, product.url);
          }
        } else {
          console.log('🏷️ ❌ No product match found for element');
        }
      });
    });

    // Add corner flashes
    console.log('🏷️ �🌟 Adding corner flashes to', discountedProductParents.size, 'parents');
    discountedProductParents.forEach((parent, parentIndex) => {
      console.log(`🏷️ �🌟 Processing parent [${parentIndex}] for corner flash:`, parent);
      const imageSelectors = parent.querySelectorAll('.card-image, .zen-card__image');
      console.log(`🏷️ 🖼️ Found ${imageSelectors.length} image selectors in parent`);

      imageSelectors.forEach((imageSelector, imageIndex) => {
        console.log(`🏷️ 🖼️ Processing image selector [${imageIndex}]:`, imageSelector);
        imageSelector.style.position = 'relative';

        // Check if corner flash already exists
        if (imageSelector.querySelector('.campaign-corner-flash')) {
          console.log('🏷️ ⚠️ Corner flash already exists, skipping');
          return;
        }

        const svgImg = document.createElement('img');
        svgImg.src = '/images/2024/11/08/nov_corner_flash.svg'; // Replace with your SVG URL
        svgImg.alt = 'Icon';
        svgImg.classList.add('campaign-corner-flash');
        svgImg.style.position = 'absolute';
        svgImg.style.top = '0';
        svgImg.style.right = '0';

        imageSelector.appendChild(svgImg);
        console.log('🏷️ ✨ Corner flash added to image selector');
      });
    });

    processZenHolidayContentBox(); // Process .zen-holiday__content-box last
  }, debounceDelay);
};

// Function to monitor HTTP calls, focusing on the specific POST to _search
function monitorHttpCalls() {
  console.log('🏷️ 🔍 Setting up HTTP monitoring...');
  const targetUrl = "https://evertrek.co.uk/evertrek/holiday/_search";
  console.log('🏷️ 🎯 Target URL for monitoring:', targetUrl);

  const originalOpen = XMLHttpRequest.prototype.open;
  const originalSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function (method, url, ...rest) {
    this._url = url;
    this._method = method;
    console.log('🏷️ 📡 XHR opened:', method, url);
    return originalOpen.apply(this, [method, url, ...rest]);
  };

  XMLHttpRequest.prototype.send = function (...args) {
    if (this._method === "POST" && this._url === targetUrl) {
      console.log('🏷️ 🎯 Monitoring target POST request to:', this._url);
      this.addEventListener("load", () => {
        console.log('🏷️ 📨 XHR response received, status:', this.status);
        if (this.status >= 200 && this.status < 300) {
          console.log('🏷️ ✅ Successful response, triggering handleResults');
          handleResults("XHR Response Trigger");
        } else {
          console.log('🏷️ ❌ Failed response, not triggering handleResults');
        }
      });
    }
    return originalSend.apply(this, args);
  };
}

// Initialize logging script on DOMContentLoaded
document.addEventListener("DOMContentLoaded", () => {
  console.log('🏷️ 🚀 DOM Content Loaded - initializing script');

  const searchResults = document.querySelector("#search-results");
  console.log('🏷️ 🔍 Search results element:', searchResults);

  if (!searchResults) {
    console.log('🏷️ 📄 No search results found, running initial load');
    handleResults("Initial Load");
  } else {
    console.log('🏷️ 🔍 Search results found, setting up HTTP monitoring');
    monitorHttpCalls(); // Start monitoring HTTP calls
  }

  // Use MutationObserver to dynamically detect `.zen-holiday__content-box`
  console.log('🏷️ 👀 Setting up MutationObserver for zen-holiday__content-box');
  const observer = new MutationObserver((mutations) => {
    console.log('🏷️ 🔄 DOM mutations detected:', mutations.length);
    const appPanels = document.querySelectorAll('.zen-holiday__content-box .zen-holiday__content-item:first-child .col:first-child .zen-media');
    if (appPanels.length > 0) {
      console.log("🏷️ ✅ Detected appPanels dynamically:", appPanels.length, appPanels);
      observer.disconnect(); // Stop observing after finding elements
      console.log('🏷️ 🛑 MutationObserver disconnected');
      processZenHolidayContentBox(); // Process the detected elements
    }
  });

  observer.observe(document.body, { childList: true, subtree: true });
  console.log('🏷️ 👀 MutationObserver started, watching document.body');
});